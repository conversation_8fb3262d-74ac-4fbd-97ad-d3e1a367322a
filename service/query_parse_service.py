from asyncio import TaskGroup
from typing import List

from config.prompts import TAG_KEY, ITEM_TAG_SYSTEM_PROMPT, ITEM_TAG_USER_PROMPT, REWRITE_QUERY_SYSTEM_PROMPT, \
    REWRITE_QUERY_USER_PROMPT, REWRITE_KEY, MATCH_ITEM_SYSTEM_PROMPT, MATCH_ITEM_USER_PROMPT, \
    MATCH_INTENT_SYSTEM_PROMPT, MATCH_INTENT_USER_PROMPT, OTHER_INTENT, COMPARE_INTENT, PARAMETER_INTENT, \
    SOFTWARE_USAGE_INTENT, SALE_POINT_INTENT, DEFECT_INTENT, DIGITAL_KNOWLEDGE_INTENT, SALE_CONSULTATION_INTENT, \
    TIMELINESS_INTENT, PUBLISH_INTENT, CHATTING_INTENT, NORMAL_INTENT_LIST, TRACE_SOURCE_USER_PROMPT, \
    TRACE_SOURCE_SYSTEM_PROMPT, ANSWER_SOURCE_KEY
from config.tags import SECOND_TAG_DICT
from core.enum.content_type import ContentType
from core.enum.doc_type import DocType
from core.schema.chat_request import ChatRequest
from core.enum.message_type import MessageType
from core.schema.doc_trace_source import DocTraceSource
from service.base_service import BaseService
from util.common_util import is_empty, chunk_list, not_empty
from core.processor import normalize_item_name
from util.string_util import parse_faq_regex, remove_first_line_if_match


class QueryParseService(BaseService):
    def __init__(self,
                 api_key_json,  # old api key for tag_question_second
                 api_key_json2,  # new api key for recognize_item_names
                 api_key_json3,  # new api key for tag_question_first
                 model_manager,
                 item_name_list,
                 normalized_item_name_list,
                 item_name_xiaomi_list,
                 redis_manager=None):

        super().__init__(None, None)
        # ToDo(hm): key 应该放到 model manager 中，但如何指导它用哪个 key 呢？（先不着急切，等下次迭代在改造）
        self._api_key_json = api_key_json
        self._api_key_json2 = api_key_json2
        self._api_key_json3 = api_key_json3
        self._model_manager = model_manager
        self._item_name_list = item_name_list
        self._normalized_item_name_list = normalized_item_name_list
        self._item_name_xiaomi_list = item_name_xiaomi_list
        self._redis_manager = redis_manager

    async def filter_by_first_tags_430(self, chat_request: ChatRequest, tg: TaskGroup):
        # TODO(jxy) 仅用于430版本的支持，后续将要删去
        # 这两个不适合放在一个 prompt 因为他们会相互干扰：打一级标签的时候会提示「当前的问题是针对 xxx」，但这个会影响实体的提取
        task = tg.create_task(self.recognize_item_names_exact(chat_request))
        item_name_dict = await task
        actual_item_names = list()
        for raw_item_name in item_name_dict:
            for item_name in item_name_dict[raw_item_name]:
                actual_item_names.append(item_name)
        tag_question_first_task = tg.create_task(self.tag_question_first(chat_request))
        first_tags_normalized = await tag_question_first_task
        chat_request.logger.info(f"selected_item_name: {chat_request.item_name}")
        chat_request.logger.info(
            f"模型推理结果: actual_item_names===> {actual_item_names}; first tag===> {first_tags_normalized}"
        )

        is_not_in_scope, _ = self.should_filter_by_first_tags(first_tags_normalized)
        is_dynamic_change, _ = self.is_dynamic_change_tags(first_tags_normalized)

        query_filter_result = MessageType.TEXT
        if is_not_in_scope:
            query_filter_result = MessageType.NOT_IN_SCOPE

        if is_dynamic_change:
            query_filter_result = MessageType.DYNAMIC_CHANGE

        if not self._match_selected_item_names(chat_request.item_name_normalized, actual_item_names):
            query_filter_result = MessageType.NOT_MATCH_ITEM
        self.log_elapse("根据一级标签进行过滤返回", chat_request)
        return query_filter_result, actual_item_names, first_tags_normalized

    def get_item_name_chunks(self, mode):
        # if mode == "exact":
        return chunk_list(self._item_name_list, 100)
        # if mode == "fuzzy":
        #     # ToDo(hm): 因为模糊机型被确认后会被吸顶，而只有小米机型可以被吸顶，所以这里只用小米的机型
        #     return chunk_list(self._item_name_xiaomi_list, 100)
        # raise RuntimeError(f"get_item_name_chunks mode={mode} not supported")

    async def recognize_language(self, chat_request: ChatRequest):
        # ToDo(hm): 暂时去掉语言的识别，等后面有需求了再说，识别语言注意对 str_message_list 和 joined_str_message 的影响
        # start_time = time.time()
        # candidates_for_language = Language.get_supported_languages()
        # question_cn = chat_request.str_message_list[-1]
        # system_prompt_template = self.prompts["prompts"]["recognize_language"]["system"]
        # user_prompt_template = self.prompts["prompts"]["recognize_language"]["user"]
        # system_prompt = system_prompt_template.format(candidates_for_language=candidates_for_language)
        # user_prompt = user_prompt_template.format(question_cn=question_cn)
        # language = chat_request.language
        # is_success, response, total_tokens_recognize_language = await self._model_manager.call_llm_with_json(
        #     system_prompt, user_prompt, self._api_key_json, chat_request)
        # if is_success and "语言" in response:
        #     recognized_language = response["语言"]
        #     if not isinstance(recognized_language, list):
        #         recognized_language = [recognized_language]
        #     recognized_language = [cur_language for cur_language in recognized_language if
        #                            cur_language in candidates_for_language]
        #     if len(recognized_language) > 0:
        #         language = Language.from_string(recognized_language[0])
        # chat_request.logger.debug(f"recognize language: {language}")
        # assert isinstance(language, Language)
        return chat_request.language, 0, 0

    async def recognize_tags(self, chat_request: ChatRequest):
        label_part = ",".join(SECOND_TAG_DICT.keys())
        system_prompt = ITEM_TAG_SYSTEM_PROMPT
        user_prompt = ITEM_TAG_USER_PROMPT.format(history_messages=chat_request.joined_str_message,
                                                  label_part=label_part)
        chat_request.logger.info(f"recognize_tags system_promp===> {system_prompt}")
        chat_request.logger.info(f"recognize_tags user_promp===> {user_prompt}")
        is_success, response = await self._model_manager.call_llm_with_json(
            system_prompt, user_prompt, self._api_key_json, chat_request)
        second_tag_list = []
        if not is_success:
            return second_tag_list

        if TAG_KEY in response:
            second_tag_list = response[TAG_KEY]
        return second_tag_list

    async def rewrite_query(self, chat_request: ChatRequest):
        system_prompt = REWRITE_QUERY_SYSTEM_PROMPT
        user_prompt = REWRITE_QUERY_USER_PROMPT.format(history_messages=chat_request.joined_str_message)
        chat_request.logger.info(f"rewrite_query system_promp===> {system_prompt}")
        chat_request.logger.info(f"rewrite_query user_promp===> {user_prompt}")
        is_success, response = await self._model_manager.call_llm_with_json(
            system_prompt, user_prompt, self._api_key_json3, chat_request)
        chat_request.logger.info(f"rewrite_query response===> {response}")
        return response[REWRITE_KEY]

    async def recognize_item_names_exact(self, chat_request: ChatRequest):
        system_prompt = MATCH_ITEM_SYSTEM_PROMPT
        user_prompt = MATCH_ITEM_USER_PROMPT.format(item_names=",".join(self._item_name_list),
                                                    history_messages=chat_request.joined_str_message)
        chat_request.logger.info(f"recognize_item_names_exact system_prompt===> {system_prompt}")
        chat_request.logger.info(f"recognize_item_names_exact user_prompt===> {user_prompt}")
        is_success, item_name_dict = await self._model_manager.call_llm_with_json(system_prompt, user_prompt,
                                                                                  self._api_key_json2, chat_request)
        if not is_success:
            return list()

        return item_name_dict

    async def trace_source(self, chat_request, response_content) -> List[DocTraceSource]:
        if chat_request.trace_source_info is None:
            return list()

        doc_trace_source_list = list()
        if not_empty(chat_request.trace_source_info.intro_list):
            intro_str = '\n'.join(chat_request.trace_source_info.intro_list)
            doc_trace_source = DocTraceSource(item_id=chat_request.item_id, item_name=chat_request.item_name,
                                              doc_type=DocType.PRODUCT_INTRODUCTION,
                                              # ToDo(hm): 考虑多语言问题
                                              title="产品详情",
                                              content_type=ContentType.KEY,
                                              content=chat_request.item_name,
                                              raw_content=intro_str)
            doc_trace_source_list.append(doc_trace_source)
        if not_empty(chat_request.trace_source_info.param_list):
            param_str = '\n'.join(chat_request.trace_source_info.param_list)
            doc_trace_source = DocTraceSource(item_id=chat_request.item_id, item_name=chat_request.item_name,
                                              doc_type=DocType.PRODUCT_PARAM,
                                              # ToDo(hm): 考虑多语言问题
                                              title="产品参数",
                                              content_type=ContentType.KEY,
                                              content=chat_request.item_name,
                                              raw_content=param_str)
            doc_trace_source_list.append(doc_trace_source)
        if not_empty(chat_request.trace_source_info.sale_tools_list):
            sale_tools_str = '\n'.join(chat_request.trace_source_info.sale_tools_list)
            doc_trace_source = DocTraceSource(item_id=chat_request.item_id, item_name=chat_request.item_name,
                                              doc_type=DocType.SALE_TOOLS,
                                              # ToDo(hm): 考虑多语言问题
                                              title="销售工具",
                                              content_type=ContentType.KEY,
                                              content=chat_request.item_name,
                                              raw_content=sale_tools_str)
            doc_trace_source_list.append(doc_trace_source)
        faq_idx = 1
        for faq_xml in chat_request.trace_source_info.faq_list:
            qa_list = parse_faq_regex(faq_xml)
            for question, answer in qa_list:
                doc_trace_source = DocTraceSource(item_id=chat_request.item_id, item_name=chat_request.item_name,
                                                  doc_type=DocType.FAQ,
                                                  title=f"FAQ-{faq_idx}",
                                                  content_type=ContentType.KEY,
                                                  # ToDo(hm): 这里应该从 mysql 中召回该问题对应的 html
                                                  content=question,
                                                  raw_content=faq_xml)
                faq_idx += 1
                doc_trace_source_list.append(doc_trace_source)
        # 答案溯源用的答案需要去掉「如思」部分
        response_content = remove_first_line_if_match(response_content)
        intent = chat_request.get_intent_prompt()
        chat_context = chat_request.joined_str_message
        reference_doc_str_list = list()
        doc_idx = 0
        for doc_trace_source in doc_trace_source_list:
            reference_doc_str_list.append(f"{doc_idx}: {doc_trace_source.raw_content}")
            doc_idx += 1
        reference_doc_str = "\n".join(reference_doc_str_list)
        user_prompt = TRACE_SOURCE_USER_PROMPT.format(item_name=chat_request.item_name, chat_context=chat_context,
                                                      answer_intent=intent, answer_content=response_content,
                                                      reference_doc=reference_doc_str)
        is_success, response = await self._model_manager.call_llm_with_json(
            TRACE_SOURCE_SYSTEM_PROMPT, user_prompt, self._api_key_json3, chat_request)
        if not is_success or is_empty(response):
            return list()

        filtered_doc_trace_source_list = list()
        for idx in response[ANSWER_SOURCE_KEY]:
            filtered_doc_trace_source_list.append(doc_trace_source_list[idx])
        return filtered_doc_trace_source_list

    async def tag_question_first(self, chat_request: ChatRequest):
        system_prompt = MATCH_INTENT_SYSTEM_PROMPT
        user_prompt = MATCH_INTENT_USER_PROMPT.format(history_messages=chat_request.joined_str_message)
        chat_request.logger.info(f"first tag system_promp===> {system_prompt}")
        chat_request.logger.info(f"first tag user_promp===> {user_prompt}")
        is_success, response = await self._model_manager.call_llm_with_json(
            system_prompt, user_prompt, self._api_key_json3, chat_request)
        if not is_success or is_empty(response):
            return OTHER_INTENT

        chat_request.logger.info(f"first tag response===> {response}")
        # ToDo(hm): from here 这里再增加一些异常处理
        first_key = list(response.keys())[0]
        first_tags_normalized = list()
        for tag in response[first_key]:
            normalized = self._normalize_tag(tag)
            if normalized is None:
                chat_request.logger.warning(f"大模型返回一个非法的 tag: {tag}")
                continue

            first_tags_normalized.append(normalized)
        if len(first_tags_normalized) == 0:
            return OTHER_INTENT

        # 双机对比优先级最高
        if COMPARE_INTENT in first_tags_normalized:
            return COMPARE_INTENT

        # 其余的则取第一个（靠大模型排优先级）
        return first_tags_normalized[0]

    @staticmethod
    def _normalize_tag(tag):
        if "对比" in tag:
            tag = COMPARE_INTENT
        if "规格" in tag or "参数" in tag:
            tag = PARAMETER_INTENT
        if "使用" in tag or "软件" in tag:
            tag = SOFTWARE_USAGE_INTENT
        if "卖点" in tag:
            tag = SALE_POINT_INTENT
        if "缺点" in tag:
            tag = DEFECT_INTENT
        if "数码知识" in tag:
            tag = DIGITAL_KNOWLEDGE_INTENT
        if "售前" in tag or "售后" in tag:
            tag = SALE_CONSULTATION_INTENT
        if "时效" in tag:
            tag = TIMELINESS_INTENT
        if "上市" in tag or "发布" in tag:
            tag = PUBLISH_INTENT
        if "闲聊" in tag:
            tag = CHATTING_INTENT
        if "其他" in tag:
            tag = OTHER_INTENT
        if tag not in NORMAL_INTENT_LIST:
            tag = OTHER_INTENT
        return tag

    @staticmethod
    def get_second_tag_map_dict_from_tag_list_and_language(second_tag_list, language):
        second_tag_map_dict = dict()
        for second_tag in second_tag_list:
            if second_tag in SECOND_TAG_DICT:
                second_tag_map_dict[second_tag] = SECOND_TAG_DICT[second_tag][language]
        return second_tag_map_dict

    @staticmethod
    def should_filter_by_first_tags(normalized_tags):
        return is_empty(normalized_tags), "非手机咨询问题"

    @staticmethod
    def is_dynamic_change_tags(normalized_tags):
        if "销售信息" in normalized_tags:
            return True, "属于动态变化中的问题"

        return False, "不属于动态变化中的问题"

    @staticmethod
    def _match_selected_item_names(expected_item_name, actual_item_names):
        if is_empty(actual_item_names):
            return True

        for name in actual_item_names:
            if expected_item_name == normalize_item_name(name):
                return True

        return False

    @property
    def item_name_list(self):
        return self._item_name_list
